package com.kbuilder.ai.provider

import com.kbuilder.ai.model.*

/**
 * AI服务提供商接口
 */
interface AIProvider {
    /**
     * 提供商名称
     */
    val name: String
    
    /**
     * 支持的模型列表
     */
    val supportedModels: List<AIModel>
    
    /**
     * 发送AI请求
     */
    suspend fun sendRequest(request: AIRequest): AIResponse
    
    /**
     * 发送流式AI请求
     */
    suspend fun sendStreamRequest(request: AIRequest): kotlinx.coroutines.flow.Flow<String>
    
    /**
     * 测试服务连接
     */
    suspend fun testConnection(): ServiceHealth
    
    /**
     * 获取默认模型
     */
    fun getDefaultModel(): AIModel
    
    /**
     * 根据名称获取模型
     */
    fun getModel(modelName: String): AIModel?
}

/**
 * AI服务提供商工厂
 */
interface AIProviderFactory {
    /**
     * 创建AI服务提供商实例
     */
    fun createProvider(config: ProviderConfig): AIProvider
    
    /**
     * 支持的提供商类型
     */
    val supportedProviders: List<String>
}

/**
 * 提供商配置
 */
data class ProviderConfig(
    val type: String,
    val apiKey: String,
    val baseUrl: String? = null,
    val timeout: Long = 30000,
    val maxRetries: Int = 3,
    val retryDelay: Long = 1000,
    val customHeaders: Map<String, String> = emptyMap()
)
