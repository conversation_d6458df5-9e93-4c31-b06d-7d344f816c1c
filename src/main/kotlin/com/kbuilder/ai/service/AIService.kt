package com.kbuilder.ai.service

import com.kbuilder.ai.model.*
import com.kbuilder.ai.provider.AIProvider
import com.kbuilder.ai.provider.ProviderConfig
import com.kbuilder.ai.provider.openai.OpenAIProvider

/**
 * AI服务管理器
 */
interface AIService {
    /**
     * 注册AI服务提供商
     */
    fun registerProvider(config: ProviderConfig): AIProvider
    
    /**
     * 获取AI服务提供商
     */
    fun getProvider(providerName: String): AIProvider?
    
    /**
     * 获取默认提供商
     */
    fun getDefaultProvider(): AIProvider?
    
    /**
     * 设置默认提供商
     */
    fun setDefaultProvider(providerName: String)
    
    /**
     * 获取所有已注册的提供商
     */
    fun getAllProviders(): Map<String, AIProvider>
    
    /**
     * 发送AI请求（使用默认提供商）
     */
    suspend fun ask(prompt: String, model: String? = null): AIResponse
    
    /**
     * 发送流式AI请求（使用默认提供商）
     */
    suspend fun askStream(prompt: String, model: String? = null): kotlinx.coroutines.flow.Flow<String>
    
    /**
     * 测试所有提供商连接
     */
    suspend fun testAllConnections(): Map<String, ServiceHealth>
    
    /**
     * 测试指定提供商连接
     */
    suspend fun testConnection(providerName: String): ServiceHealth?
}

/**
 * AI服务管理器实现
 */
class AIServiceImpl : AIService {
    private val providers = mutableMapOf<String, AIProvider>()
    private var defaultProviderName: String? = null
    
    override fun registerProvider(config: ProviderConfig): AIProvider {
        val provider = when (config.type.lowercase()) {
            "openai" -> OpenAIProvider(config)
            else -> throw IllegalArgumentException("Unsupported provider type: ${config.type}")
        }

        providers[provider.name] = provider

        // 如果这是第一个提供商，设置为默认
        if (defaultProviderName == null) {
            defaultProviderName = provider.name
        }

        return provider
    }
    
    override fun getProvider(providerName: String): AIProvider? {
        return providers[providerName]
    }
    
    override fun getDefaultProvider(): AIProvider? {
        return defaultProviderName?.let { providers[it] }
    }
    
    override fun setDefaultProvider(providerName: String) {
        if (providers.containsKey(providerName)) {
            defaultProviderName = providerName
        } else {
            throw IllegalArgumentException("Provider '$providerName' not found")
        }
    }
    
    override fun getAllProviders(): Map<String, AIProvider> {
        return providers.toMap()
    }
    
    override suspend fun ask(prompt: String, model: String?): AIResponse {
        val provider = getDefaultProvider() 
            ?: throw IllegalStateException("No default provider configured")
        
        val aiModel = model?.let { provider.getModel(it) } ?: provider.getDefaultModel()
        val request = AIRequest(prompt = prompt, model = aiModel)
        
        return provider.sendRequest(request)
    }
    
    override suspend fun askStream(prompt: String, model: String?): kotlinx.coroutines.flow.Flow<String> {
        val provider = getDefaultProvider() 
            ?: throw IllegalStateException("No default provider configured")
        
        val aiModel = model?.let { provider.getModel(it) } ?: provider.getDefaultModel()
        val request = AIRequest(prompt = prompt, model = aiModel, stream = true)
        
        return provider.sendStreamRequest(request)
    }
    
    override suspend fun testAllConnections(): Map<String, ServiceHealth> {
        return providers.mapValues { (_, provider) ->
            try {
                provider.testConnection()
            } catch (e: Exception) {
                ServiceHealth(
                    isHealthy = false,
                    provider = provider.name,
                    responseTime = -1,
                    error = e.message
                )
            }
        }
    }
    
    override suspend fun testConnection(providerName: String): ServiceHealth? {
        return providers[providerName]?.testConnection()
    }
}
