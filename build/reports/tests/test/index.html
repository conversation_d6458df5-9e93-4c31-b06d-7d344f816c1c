<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Test Summary</title>
<link href="css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="css/style.css" rel="stylesheet" type="text/css"/>
<script src="js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Test Summary</h1>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">25</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">8</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.181s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">68%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Failed tests</a>
</li>
<li>
<a href="#">Packages</a>
</li>
<li>
<a href="#">Classes</a>
</li>
</ul>
<div class="tab">
<h2>Failed tests</h2>
<ul class="linkList">
<li>
<a href="classes/com.kbuilder.cli.AskCommandTest.html">AskCommandTest</a>.
<a href="classes/com.kbuilder.cli.AskCommandTest.html#should enable streaming when stream option is given()">should enable streaming when stream option is given()</a>
</li>
<li>
<a href="classes/com.kbuilder.cli.AskCommandTest.html">AskCommandTest</a>.
<a href="classes/com.kbuilder.cli.AskCommandTest.html#should print AI response when ask command is executed with question()">should print AI response when ask command is executed with question()</a>
</li>
<li>
<a href="classes/com.kbuilder.cli.AskCommandTest.html">AskCommandTest</a>.
<a href="classes/com.kbuilder.cli.AskCommandTest.html#should show help for ask command()">should show help for ask command()</a>
</li>
<li>
<a href="classes/com.kbuilder.cli.AskCommandTest.html">AskCommandTest</a>.
<a href="classes/com.kbuilder.cli.AskCommandTest.html#should use specific model when model option is given()">should use specific model when model option is given()</a>
</li>
<li>
<a href="classes/com.kbuilder.cli.TestConnectionCommandTest.html">TestConnectionCommandTest</a>.
<a href="classes/com.kbuilder.cli.TestConnectionCommandTest.html#should print connection test results when test-connection command is executed()">should print connection test results when test-connection command is executed()</a>
</li>
<li>
<a href="classes/com.kbuilder.cli.TestConnectionCommandTest.html">TestConnectionCommandTest</a>.
<a href="classes/com.kbuilder.cli.TestConnectionCommandTest.html#should show help for test-connection command()">should show help for test-connection command()</a>
</li>
<li>
<a href="classes/com.kbuilder.cli.TestConnectionCommandTest.html">TestConnectionCommandTest</a>.
<a href="classes/com.kbuilder.cli.TestConnectionCommandTest.html#should test specific provider when provider option is given()">should test specific provider when provider option is given()</a>
</li>
<li>
<a href="classes/com.kbuilder.config.ConfigurationTest.html">ConfigurationTest</a>.
<a href="classes/com.kbuilder.config.ConfigurationTest.html#should check config file existence()">should check config file existence()</a>
</li>
</ul>
</div>
<div class="tab">
<h2>Packages</h2>
<table>
<thead>
<tr>
<th>Package</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="packages/com.kbuilder.ai.service.html">com.kbuilder.ai.service</a>
</td>
<td>7</td>
<td>0</td>
<td>0</td>
<td>0.092s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="failures">
<a href="packages/com.kbuilder.cli.html">com.kbuilder.cli</a>
</td>
<td>13</td>
<td>7</td>
<td>0</td>
<td>0.077s</td>
<td class="failures">46%</td>
</tr>
<tr>
<td class="failures">
<a href="packages/com.kbuilder.config.html">com.kbuilder.config</a>
</td>
<td>5</td>
<td>1</td>
<td>0</td>
<td>0.012s</td>
<td class="failures">80%</td>
</tr>
</tbody>
</table>
</div>
<div class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="classes/com.kbuilder.ai.service.AIServiceTest.html">com.kbuilder.ai.service.AIServiceTest</a>
</td>
<td>7</td>
<td>0</td>
<td>0</td>
<td>0.092s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="failures">
<a href="classes/com.kbuilder.cli.AskCommandTest.html">com.kbuilder.cli.AskCommandTest</a>
</td>
<td>5</td>
<td>4</td>
<td>0</td>
<td>0.063s</td>
<td class="failures">20%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.kbuilder.cli.GenerateCommandTest.html">com.kbuilder.cli.GenerateCommandTest</a>
</td>
<td>2</td>
<td>0</td>
<td>0</td>
<td>0.002s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.kbuilder.cli.KBuilderCliTest.html">com.kbuilder.cli.KBuilderCliTest</a>
</td>
<td>3</td>
<td>0</td>
<td>0</td>
<td>0.003s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="failures">
<a href="classes/com.kbuilder.cli.TestConnectionCommandTest.html">com.kbuilder.cli.TestConnectionCommandTest</a>
</td>
<td>3</td>
<td>3</td>
<td>0</td>
<td>0.009s</td>
<td class="failures">0%</td>
</tr>
<tr>
<td class="failures">
<a href="classes/com.kbuilder.config.ConfigurationTest.html">com.kbuilder.config.ConfigurationTest</a>
</td>
<td>5</td>
<td>1</td>
<td>0</td>
<td>0.012s</td>
<td class="failures">80%</td>
</tr>
</tbody>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.1</a> at 2025年6月17日 13:07:58</p>
</div>
</div>
</body>
</html>
