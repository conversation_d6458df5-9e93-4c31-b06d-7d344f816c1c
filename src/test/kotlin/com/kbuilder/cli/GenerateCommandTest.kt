package com.kbuilder.cli

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import picocli.CommandLine
import java.io.ByteArrayOutputStream
import java.io.PrintWriter

class GenerateCommandTest {

    @Test
    fun `should print placeholder message when generate command is executed`() {
        // Arrange
        val outputStream = ByteArrayOutputStream()
        val printWriter = PrintWriter(outputStream)
        val cli = KBuilderCli()
        val commandLine = CommandLine(cli)
        commandLine.out = printWriter

        // Act
        val exitCode = commandLine.execute("generate")

        // Assert
        assertEquals(0, exitCode)
        printWriter.flush()
        val output = outputStream.toString().trim()
        assertEquals("Generate command received.", output)
    }

    @Test
    fun `should execute generate command directly`() {
        // Arrange
        val outputStream = ByteArrayOutputStream()
        val printWriter = PrintWriter(outputStream)
        val generateCommand = GenerateCommand()
        val commandLine = CommandLine(generateCommand)
        commandLine.out = printWriter

        // Act
        val exitCode = commandLine.execute()

        // Assert
        assertEquals(0, exitCode)
        printWriter.flush()
        val output = outputStream.toString().trim()
        assertEquals("Generate command received.", output)
    }
}
