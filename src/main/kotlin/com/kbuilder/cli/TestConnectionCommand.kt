package com.kbuilder.cli

import com.kbuilder.ai.service.AIService
import com.kbuilder.ai.service.AIServiceImpl
import com.kbuilder.ai.provider.ProviderConfig
import com.kbuilder.config.ConfigurationManager
import kotlinx.coroutines.runBlocking
import picocli.CommandLine
import picocli.CommandLine.*

@Command(
    name = "test-connection",
    description = ["Test AI service connections"],
    mixinStandardHelpOptions = true
)
class TestConnectionCommand : Runnable {
    
    @Spec
    lateinit var spec: CommandLine.Model.CommandSpec
    
    @Option(
        names = ["-p", "--provider"],
        description = ["Test specific provider (default: test all)"]
    )
    var providerName: String? = null
    
    @Option(
        names = ["-v", "--verbose"],
        description = ["Show detailed connection information"]
    )
    var verbose: Boolean = false
    
    private val configManager = ConfigurationManager()
    private val aiService: AIService = AIServiceImpl()
    
    override fun run() {
        val out = spec.commandLine().out
        
        try {
            // Load configuration
            val config = configManager.loadConfig()
            
            if (config.providers.isEmpty()) {
                out.println("No AI providers configured.")
                out.println("Please run 'kbuilder config' to set up providers.")
                return
            }
            
            // Register providers
            config.providers.forEach { (name, providerConfig) ->
                try {
                    val providerConfigObj = ProviderConfig(
                        type = providerConfig.type,
                        apiKey = providerConfig.apiKey,
                        baseUrl = providerConfig.baseUrl,
                        timeout = providerConfig.timeout,
                        maxRetries = providerConfig.maxRetries,
                        retryDelay = providerConfig.retryDelay,
                        customHeaders = providerConfig.customHeaders
                    )
                    aiService.registerProvider(providerConfigObj)
                } catch (e: Exception) {
                    if (verbose) {
                        out.println("Failed to register provider '$name': ${e.message}")
                    }
                }
            }
            
            // Test connections
            runBlocking {
                if (providerName != null) {
                    testSpecificProvider(out, providerName!!)
                } else {
                    testAllProviders(out)
                }
            }
            
        } catch (e: Exception) {
            out.println("Error testing connections: ${e.message}")
            if (verbose) {
                e.printStackTrace()
            }
        }
    }
    
    private suspend fun testSpecificProvider(out: java.io.PrintWriter, provider: String) {
        out.println("Testing connection to provider: $provider")
        out.println("=" * 50)
        
        val result = aiService.testConnection(provider)
        if (result != null) {
            printConnectionResult(out, provider, result)
        } else {
            out.println("Provider '$provider' not found or not configured.")
        }
    }
    
    private suspend fun testAllProviders(out: java.io.PrintWriter) {
        out.println("Testing AI service connections...")
        out.println("=" * 50)
        
        val results = aiService.testAllConnections()
        
        if (results.isEmpty()) {
            out.println("No providers registered.")
            return
        }
        
        results.forEach { (provider, health) ->
            printConnectionResult(out, provider, health)
            out.println("-" * 30)
        }
        
        val healthyCount = results.values.count { it.isHealthy }
        val totalCount = results.size
        
        out.println()
        out.println("Summary: $healthyCount/$totalCount providers are healthy")
    }
    
    private fun printConnectionResult(
        out: java.io.PrintWriter, 
        provider: String, 
        health: com.kbuilder.ai.model.ServiceHealth
    ) {
        out.println("Provider: $provider")
        out.println("Status: ${if (health.isHealthy) "✓ Healthy" else "✗ Unhealthy"}")
        
        if (health.responseTime >= 0) {
            out.println("Response Time: ${health.responseTime}ms")
        }
        
        if (health.error != null) {
            out.println("Error: ${health.error}")
        }
        
        if (verbose && health.isHealthy) {
            out.println("Connection successful - provider is ready to use")
        }
    }
}

// Extension function for string repetition
private operator fun String.times(n: Int): String = this.repeat(n)
