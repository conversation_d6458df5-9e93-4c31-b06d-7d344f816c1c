package com.kbuilder.ai.service

import com.kbuilder.ai.model.*
import com.kbuilder.ai.provider.AIProvider
import com.kbuilder.ai.provider.ProviderConfig
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach

class AIServiceTest {
    
    private lateinit var aiService: AIService
    private lateinit var mockProvider: MockAIProvider
    
    @BeforeEach
    fun setUp() {
        aiService = AIServiceImpl()
        mockProvider = MockAIProvider()
    }
    
    @Test
    fun `should register AI provider successfully`() = runTest {
        // Arrange
        val config = ProviderConfig(
            type = "openai",
            apiKey = "test-key"
        )

        // Act
        val provider = aiService.registerProvider(config)

        // Assert
        assertNotNull(provider)
        assertEquals("openai", provider.name)
        assertEquals(provider, aiService.getProvider("openai"))
        assertEquals(provider, aiService.getDefaultProvider())
    }
    
    @Test
    fun `should get registered provider`() = runTest {
        // This test will fail until we implement the registration
        val provider = aiService.getProvider("openai")
        assertNull(provider) // Should be null since nothing is registered yet
    }
    
    @Test
    fun `should set and get default provider`() = runTest {
        // This should fail until we implement provider registration
        assertThrows(IllegalArgumentException::class.java) {
            aiService.setDefaultProvider("nonexistent")
        }
    }
    
    @Test
    fun `should ask question using default provider`() = runTest {
        // This should fail until we have a default provider
        try {
            aiService.ask("Hello, AI!")
            fail("Expected IllegalStateException")
        } catch (e: IllegalStateException) {
            assertEquals("No default provider configured", e.message)
        }
    }

    @Test
    fun `should ask stream question using default provider`() = runTest {
        // This should fail until we have a default provider
        try {
            aiService.askStream("Hello, AI!")
            fail("Expected IllegalStateException")
        } catch (e: IllegalStateException) {
            assertEquals("No default provider configured", e.message)
        }
    }
    
    @Test
    fun `should test all connections`() = runTest {
        // Should return empty map when no providers are registered
        val results = aiService.testAllConnections()
        assertTrue(results.isEmpty())
    }
    
    @Test
    fun `should test specific connection`() = runTest {
        // Should return null when provider doesn't exist
        val result = aiService.testConnection("nonexistent")
        assertNull(result)
    }
}

/**
 * Mock AI Provider for testing
 */
class MockAIProvider : AIProvider {
    override val name: String = "mock"
    override val supportedModels: List<AIModel> = listOf(
        AIModel(name = "mock-model", provider = "mock")
    )
    
    override suspend fun sendRequest(request: AIRequest): AIResponse {
        return AIResponse(
            content = "Mock response to: ${request.prompt}",
            model = request.model.name,
            usage = TokenUsage(10, 20, 30)
        )
    }
    
    override suspend fun sendStreamRequest(request: AIRequest) = flowOf(
        "Mock ", "stream ", "response"
    )
    
    override suspend fun testConnection(): ServiceHealth {
        return ServiceHealth(
            isHealthy = true,
            provider = name,
            responseTime = 100
        )
    }
    
    override fun getDefaultModel(): AIModel {
        return supportedModels.first()
    }
    
    override fun getModel(modelName: String): AIModel? {
        return supportedModels.find { it.name == modelName }
    }
}
