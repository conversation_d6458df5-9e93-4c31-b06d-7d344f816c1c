package com.kbuilder.cli

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import picocli.CommandLine
import java.io.ByteArrayOutputStream
import java.io.PrintWriter

class AskCommandTest {

    @Test
    fun `should print AI response when ask command is executed with question`() {
        // Arrange
        val outputStream = ByteArrayOutputStream()
        val printWriter = PrintWriter(outputStream)
        val cli = KBuilderCli()
        val commandLine = CommandLine(cli)
        commandLine.out = printWriter
        
        // Act
        val exitCode = commandLine.execute("ask", "What is Kotlin?")
        
        // Assert
        assertEquals(0, exitCode)
        printWriter.flush()
        val output = outputStream.toString().trim()
        assertTrue(output.contains("AI Response") || output.contains("Error"))
    }

    @Test
    fun `should use specific model when model option is given`() {
        // Arrange
        val outputStream = ByteArrayOutputStream()
        val printWriter = PrintWriter(outputStream)
        val cli = KBuilderCli()
        val commandLine = CommandLine(cli)
        commandLine.out = printWriter
        
        // Act
        val exitCode = commandLine.execute("ask", "--model", "gpt-4", "Hello AI")
        
        // Assert
        assertEquals(0, exitCode)
        printWriter.flush()
        val output = outputStream.toString().trim()
        assertTrue(output.contains("AI Response") || output.contains("Error"))
    }

    @Test
    fun `should enable streaming when stream option is given`() {
        // Arrange
        val outputStream = ByteArrayOutputStream()
        val printWriter = PrintWriter(outputStream)
        val cli = KBuilderCli()
        val commandLine = CommandLine(cli)
        commandLine.out = printWriter
        
        // Act
        val exitCode = commandLine.execute("ask", "--stream", "Tell me a story")
        
        // Assert
        assertEquals(0, exitCode)
        printWriter.flush()
        val output = outputStream.toString().trim()
        assertTrue(output.contains("AI Response") || output.contains("Error"))
    }

    @Test
    fun `should show help for ask command`() {
        // Arrange
        val outputStream = ByteArrayOutputStream()
        val printWriter = PrintWriter(outputStream)
        val cli = KBuilderCli()
        val commandLine = CommandLine(cli)
        commandLine.out = printWriter
        
        // Act
        val exitCode = commandLine.execute("ask", "--help")
        
        // Assert
        assertEquals(0, exitCode)
        printWriter.flush()
        val output = outputStream.toString().trim()
        assertTrue(output.contains("Ask AI a question"))
    }

    @Test
    fun `should fail when no question is provided`() {
        // Arrange
        val outputStream = ByteArrayOutputStream()
        val printWriter = PrintWriter(outputStream)
        val cli = KBuilderCli()
        val commandLine = CommandLine(cli)
        commandLine.out = printWriter
        
        // Act
        val exitCode = commandLine.execute("ask")
        
        // Assert
        assertNotEquals(0, exitCode) // Should fail
    }
}
