package com.kbuilder.cli

import com.kbuilder.ai.service.AIService
import com.kbuilder.ai.service.AIServiceImpl
import com.kbuilder.ai.provider.ProviderConfig
import com.kbuilder.config.ConfigurationManager
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.runBlocking
import picocli.CommandLine
import picocli.CommandLine.*

@Command(
    name = "ask",
    description = ["Ask AI a question"],
    mixinStandardHelpOptions = true
)
class AskCommand : Runnable {
    
    @Spec
    lateinit var spec: CommandLine.Model.CommandSpec
    
    @Parameters(
        index = "0..*",
        description = ["The question to ask the AI"],
        arity = "1..*"
    )
    var question: Array<String> = arrayOf()
    
    @Option(
        names = ["-m", "--model"],
        description = ["Specify the AI model to use"]
    )
    var model: String? = null
    
    @Option(
        names = ["-s", "--stream"],
        description = ["Enable streaming response"]
    )
    var stream: Boolean = false
    
    @Option(
        names = ["-p", "--provider"],
        description = ["Specify the AI provider to use"]
    )
    var provider: String? = null
    
    @Option(
        names = ["-v", "--verbose"],
        description = ["Show detailed information"]
    )
    var verbose: Boolean = false
    
    @Option(
        names = ["--system"],
        description = ["System message to set context"]
    )
    var systemMessage: String? = null
    
    private val configManager = ConfigurationManager()
    private val aiService: AIService = AIServiceImpl()
    
    override fun run() {
        val out = spec.commandLine().out
        
        if (question.isEmpty()) {
            out.println("Error: Please provide a question to ask the AI.")
            out.println("Usage: kbuilder ask \"Your question here\"")
            spec.commandLine().usage(out)
            return
        }
        
        val questionText = question.joinToString(" ")
        
        try {
            // Load configuration
            val config = configManager.loadConfig()
            
            if (config.providers.isEmpty()) {
                out.println("No AI providers configured.")
                out.println("Please run 'kbuilder config' to set up providers.")
                return
            }
            
            // Register providers
            config.providers.forEach { (name, providerConfig) ->
                try {
                    val providerConfigObj = ProviderConfig(
                        type = providerConfig.type,
                        apiKey = providerConfig.apiKey,
                        baseUrl = providerConfig.baseUrl,
                        timeout = providerConfig.timeout,
                        maxRetries = providerConfig.maxRetries,
                        retryDelay = providerConfig.retryDelay,
                        customHeaders = providerConfig.customHeaders
                    )
                    aiService.registerProvider(providerConfigObj)
                } catch (e: Exception) {
                    if (verbose) {
                        out.println("Failed to register provider '$name': ${e.message}")
                    }
                }
            }
            
            // Set default provider if specified
            if (provider != null) {
                try {
                    aiService.setDefaultProvider(provider!!)
                } catch (e: Exception) {
                    out.println("Error: Provider '$provider' not found or not configured.")
                    return
                }
            }
            
            // Ask the question
            runBlocking {
                askQuestion(out, questionText)
            }
            
        } catch (e: Exception) {
            out.println("Error: ${e.message}")
            if (verbose) {
                e.printStackTrace()
            }
        }
    }
    
    private suspend fun askQuestion(out: java.io.PrintWriter, question: String) {
        try {
            if (verbose) {
                out.println("Question: $question")
                if (model != null) {
                    out.println("Model: $model")
                }
                if (systemMessage != null) {
                    out.println("System: $systemMessage")
                }
                out.println("Streaming: $stream")
                out.println("-" * 50)
            }
            
            if (stream) {
                out.println("AI Response (streaming):")
                out.println()
                
                aiService.askStream(question, model).collect { chunk ->
                    out.print(chunk)
                    out.flush()
                }
                out.println()
                
            } else {
                if (verbose) {
                    out.print("Thinking...")
                    out.flush()
                }
                
                val response = aiService.ask(question, model)
                
                if (verbose) {
                    out.println("\r" + " " * 20 + "\r") // Clear "Thinking..."
                }
                
                out.println("AI Response:")
                out.println()
                out.println(response.content)
                
                if (verbose && response.usage != null) {
                    out.println()
                    out.println("Token Usage:")
                    out.println("  Prompt: ${response.usage.promptTokens}")
                    out.println("  Completion: ${response.usage.completionTokens}")
                    out.println("  Total: ${response.usage.totalTokens}")
                }
            }
            
        } catch (e: Exception) {
            out.println("Error getting AI response: ${e.message}")
            if (verbose) {
                e.printStackTrace()
            }
        }
    }
}

// Extension function for string repetition
private operator fun String.times(n: Int): String = this.repeat(n)
