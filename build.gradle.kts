plugins {
    kotlin("jvm") version "1.9.20"
    application
}

group = "com.kbuilder"
version = "0.1.0"

repositories {
    mavenCentral()
}

dependencies {
    // CLI parsing
    implementation("info.picocli:picocli:4.7.5")
    
    // Kotlin standard library
    implementation("org.jetbrains.kotlin:kotlin-stdlib")
    
    // Testing
    testImplementation("org.junit.jupiter:junit-jupiter:5.10.0")
    testImplementation("org.junit.jupiter:junit-jupiter-engine:5.10.0")
    testImplementation("org.assertj:assertj-core:3.24.2")
    
    // For testing CLI output
    testImplementation("com.github.stefanbirkner:system-lambda:1.2.1")
}

application {
    mainClass.set("com.kbuilder.cli.MainKt")
}

tasks.test {
    useJUnitPlatform()
}

tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
    kotlinOptions {
        jvmTarget = "17"
    }
}

java {
    toolchain {
        languageVersion.set(JavaLanguageVersion.of(17))
    }
}
