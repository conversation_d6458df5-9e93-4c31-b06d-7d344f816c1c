**Project Initialization and Basic CLI Structure**

We are building an AI Coding CLI tool using **Kotlin** and **Gradle**, following **Test-Driven Development (TDD)** principles. This is the first development cycle.

Your task is to complete the following functional points:

1. **Initialize Project**: Set up a new Gradle project configured for Kotlin application development. Ensure the project structure is standard and includes necessary wrapper scripts (`gradlew`).
2. **Add CLI Parsing**: Integrate a modern command-line parsing library (such as `picocli` or `kotlinx-cli`) and create the main application entry point.
3. **Implement Basic Commands**: The CLI application must respond to two initial inputs:
    - A `--version` option that prints a hardcoded version string (e.g., "0.1.0").
    - A `generate` command that, for now, does nothing but prints a placeholder message like "Generate command received."
4. **TDD Enforcement**: Before implementing the logic for the `--version` option and the `generate` command, create acceptance tests that verify their behavior. The tests should fail initially and then pass after you implement the command logic. Configure the project to use a standard Kotlin testing framework like JUnit 5.

Please proceed with generating all necessary files and code for this initial setup.