8src/test/kotlin/com/kbuilder/ai/service/AIServiceTest.kt7src/test/kotlin/com/kbuilder/cli/GenerateCommandTest.kt3src/test/kotlin/com/kbuilder/cli/KBuilderCliTest.kt8src/test/kotlin/com/kbuilder/config/ConfigurationTest.kt2src/test/kotlin/com/kbuilder/cli/AskCommandTest.kt=src/test/kotlin/com/kbuilder/cli/TestConnectionCommandTest.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 