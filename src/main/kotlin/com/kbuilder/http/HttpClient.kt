package com.kbuilder.http

import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.plugins.logging.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import io.ktor.utils.io.*
import kotlinx.coroutines.delay
import kotlinx.serialization.json.Json
import com.kbuilder.ai.model.AIError

/**
 * HTTP客户端配置
 */
data class HttpClientConfig(
    val timeout: Long = 30000,
    val maxRetries: Int = 3,
    val retryDelay: Long = 1000,
    val enableLogging: Boolean = false
)

/**
 * 统一的HTTP客户端封装
 */
class KBuilderHttpClient(private val config: HttpClientConfig = HttpClientConfig()) {
    
    private val client = HttpClient(CIO) {
        install(ContentNegotiation) {
            json(Json {
                ignoreUnknownKeys = true
                isLenient = true
            })
        }
        
        install(HttpTimeout) {
            requestTimeoutMillis = config.timeout
            connectTimeoutMillis = config.timeout
            socketTimeoutMillis = config.timeout
        }
        
        if (config.enableLogging) {
            install(Logging) {
                logger = Logger.DEFAULT
                level = LogLevel.INFO
            }
        }
        
        defaultRequest {
            header(HttpHeaders.UserAgent, "KBuilder-CLI/1.0")
        }
    }
    
    /**
     * 发送GET请求
     */
    suspend fun get(
        url: String,
        headers: Map<String, String> = emptyMap()
    ): HttpResponse {
        return executeWithRetry {
            client.get(url) {
                headers.forEach { (key, value) ->
                    header(key, value)
                }
            }
        }
    }
    
    /**
     * 发送POST请求
     */
    suspend fun post(
        url: String,
        body: Any? = null,
        headers: Map<String, String> = emptyMap()
    ): HttpResponse {
        return executeWithRetry {
            client.post(url) {
                headers.forEach { (key, value) ->
                    header(key, value)
                }
                
                if (body != null) {
                    contentType(ContentType.Application.Json)
                    setBody(body)
                }
            }
        }
    }
    
    /**
     * 发送流式POST请求
     */
    suspend fun postStream(
        url: String,
        body: Any? = null,
        headers: Map<String, String> = emptyMap(),
        onChunk: suspend (String) -> Unit
    ) {
        executeWithRetry {
            client.preparePost(url) {
                headers.forEach { (key, value) ->
                    header(key, value)
                }
                
                if (body != null) {
                    contentType(ContentType.Application.Json)
                    setBody(body)
                }
            }.execute { response ->
                if (!response.status.isSuccess()) {
                    throw createHttpError(response)
                }
                
                val channel = response.bodyAsChannel()
                while (!channel.isClosedForRead) {
                    val chunk = channel.readUTF8Line()
                    if (chunk != null) {
                        onChunk(chunk)
                    }
                }
                response
            }
        }
    }
    
    /**
     * 带重试机制的请求执行
     */
    private suspend fun <T> executeWithRetry(request: suspend () -> T): T {
        var lastException: Exception? = null
        
        repeat(config.maxRetries) { attempt ->
            try {
                val result = request()
                if (result is HttpResponse && !result.status.isSuccess()) {
                    throw createHttpError(result)
                }
                return result
            } catch (e: Exception) {
                lastException = e
                
                // 如果是最后一次尝试，直接抛出异常
                if (attempt == config.maxRetries - 1) {
                    throw when (e) {
                        is AIError -> e
                        else -> AIError.NetworkError("Request failed after ${config.maxRetries} attempts", e)
                    }
                }
                
                // 等待后重试
                delay(config.retryDelay * (attempt + 1))
            }
        }
        
        throw AIError.NetworkError("Request failed after ${config.maxRetries} attempts", lastException)
    }
    
    /**
     * 创建HTTP错误
     */
    private suspend fun createHttpError(response: HttpResponse): AIError {
        val body = try {
            response.bodyAsText()
        } catch (e: Exception) {
            "Failed to read response body"
        }
        
        return when (response.status.value) {
            401 -> AIError.AuthenticationError("Authentication failed: $body")
            429 -> {
                val retryAfter = response.headers["Retry-After"]?.toLongOrNull()
                AIError.RateLimitError("Rate limit exceeded: $body", retryAfter)
            }
            400, 422 -> AIError.InvalidRequestError("Invalid request: $body")
            503 -> AIError.ServiceUnavailableError("Service unavailable: $body")
            else -> AIError.UnknownError("HTTP ${response.status.value}: $body")
        }
    }
    
    /**
     * 关闭客户端
     */
    fun close() {
        client.close()
    }
}
