package com.kbuilder.ai.provider.openai

import com.kbuilder.ai.model.*
import com.kbuilder.ai.provider.AIProvider
import com.kbuilder.ai.provider.ProviderConfig
import com.kbuilder.http.HttpClientConfig
import com.kbuilder.http.KBuilderHttpClient
import io.ktor.client.statement.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlin.system.measureTimeMillis

/**
 * OpenAI API请求数据结构
 */
@Serializable
data class OpenAIRequest(
    val model: String,
    val messages: List<OpenAIMessage>,
    val max_tokens: Int? = null,
    val temperature: Double? = null,
    val top_p: Double? = null,
    val frequency_penalty: Double? = null,
    val presence_penalty: Double? = null,
    val stream: Boolean = false
)

@Serializable
data class OpenAIMessage(
    val role: String,
    val content: String
)

/**
 * OpenAI API响应数据结构
 */
@Serializable
data class OpenAIResponse(
    val id: String,
    val `object`: String,
    val created: Long,
    val model: String,
    val choices: List<OpenAIChoice>,
    val usage: OpenAIUsage? = null
)

@Serializable
data class OpenAIChoice(
    val index: Int,
    val message: OpenAIMessage? = null,
    val delta: OpenAIMessage? = null,
    val finish_reason: String? = null
)

@Serializable
data class OpenAIUsage(
    val prompt_tokens: Int,
    val completion_tokens: Int,
    val total_tokens: Int
)

/**
 * OpenAI服务提供商实现
 */
class OpenAIProvider(private val config: ProviderConfig) : AIProvider {
    
    override val name: String = "openai"
    
    override val supportedModels: List<AIModel> = listOf(
        AIModel(name = "gpt-3.5-turbo", provider = name, maxTokens = 4096),
        AIModel(name = "gpt-4", provider = name, maxTokens = 8192),
        AIModel(name = "gpt-4-turbo", provider = name, maxTokens = 128000),
        AIModel(name = "gpt-4o", provider = name, maxTokens = 128000)
    )
    
    private val httpClient = KBuilderHttpClient(
        HttpClientConfig(
            timeout = config.timeout,
            maxRetries = config.maxRetries,
            retryDelay = config.retryDelay,
            enableLogging = false
        )
    )
    
    private val baseUrl = config.baseUrl ?: "https://api.openai.com/v1"
    private val json = Json { ignoreUnknownKeys = true }
    
    override suspend fun sendRequest(request: AIRequest): AIResponse {
        val openAIRequest = buildOpenAIRequest(request)
        
        val response = httpClient.post(
            url = "$baseUrl/chat/completions",
            body = openAIRequest,
            headers = buildHeaders()
        )
        
        val responseText = response.bodyAsText()
        val openAIResponse = json.decodeFromString<OpenAIResponse>(responseText)
        
        return convertToAIResponse(openAIResponse)
    }
    
    override suspend fun sendStreamRequest(request: AIRequest): Flow<String> = flow {
        val openAIRequest = buildOpenAIRequest(request.copy(stream = true))
        
        httpClient.postStream(
            url = "$baseUrl/chat/completions",
            body = openAIRequest,
            headers = buildHeaders()
        ) { chunk ->
            if (chunk.startsWith("data: ") && !chunk.contains("[DONE]")) {
                try {
                    val jsonData = chunk.removePrefix("data: ")
                    val streamResponse = json.decodeFromString<OpenAIResponse>(jsonData)
                    val content = streamResponse.choices.firstOrNull()?.delta?.content
                    if (!content.isNullOrEmpty()) {
                        emit(content)
                    }
                } catch (e: Exception) {
                    // 忽略解析错误，继续处理下一个chunk
                }
            }
        }
    }
    
    override suspend fun testConnection(): ServiceHealth {
        val responseTime = measureTimeMillis {
            try {
                val testRequest = OpenAIRequest(
                    model = "gpt-3.5-turbo",
                    messages = listOf(OpenAIMessage("user", "Hello")),
                    max_tokens = 1
                )
                
                httpClient.post(
                    url = "$baseUrl/chat/completions",
                    body = testRequest,
                    headers = buildHeaders()
                )
            } catch (e: Exception) {
                return ServiceHealth(
                    isHealthy = false,
                    provider = name,
                    responseTime = -1,
                    error = e.message
                )
            }
        }
        
        return ServiceHealth(
            isHealthy = true,
            provider = name,
            responseTime = responseTime
        )
    }
    
    override fun getDefaultModel(): AIModel {
        return supportedModels.first { it.name == "gpt-3.5-turbo" }
    }
    
    override fun getModel(modelName: String): AIModel? {
        return supportedModels.find { it.name == modelName }
    }
    
    private fun buildOpenAIRequest(request: AIRequest): OpenAIRequest {
        val messages = mutableListOf<OpenAIMessage>()
        
        // 添加系统消息
        if (!request.systemMessage.isNullOrEmpty()) {
            messages.add(OpenAIMessage("system", request.systemMessage))
        }
        
        // 添加用户消息
        messages.add(OpenAIMessage("user", request.prompt))
        
        return OpenAIRequest(
            model = request.model.name,
            messages = messages,
            max_tokens = request.maxTokens ?: request.model.maxTokens,
            temperature = request.temperature ?: request.model.temperature,
            top_p = request.model.topP,
            frequency_penalty = request.model.frequencyPenalty,
            presence_penalty = request.model.presencePenalty,
            stream = request.stream
        )
    }
    
    private fun buildHeaders(): Map<String, String> {
        return mapOf(
            "Authorization" to "Bearer ${config.apiKey}",
            "Content-Type" to "application/json"
        ) + config.customHeaders
    }
    
    private fun convertToAIResponse(openAIResponse: OpenAIResponse): AIResponse {
        val choice = openAIResponse.choices.firstOrNull()
            ?: throw AIError.InvalidRequestError("No choices in response")
        
        val content = choice.message?.content
            ?: throw AIError.InvalidRequestError("No content in response")
        
        val usage = openAIResponse.usage?.let {
            TokenUsage(
                promptTokens = it.prompt_tokens,
                completionTokens = it.completion_tokens,
                totalTokens = it.total_tokens
            )
        }
        
        return AIResponse(
            content = content,
            model = openAIResponse.model,
            usage = usage,
            finishReason = choice.finish_reason,
            id = openAIResponse.id
        )
    }
}
