%com/kbuilder/ai/service/AIServiceTestPcom/kbuilder/ai/service/AIServiceTest$should register AI provider successfully$1Fcom/kbuilder/ai/service/AIServiceTest$should get registered provider$1Kcom/kbuilder/ai/service/AIServiceTest$should set and get default provider$1Rcom/kbuilder/ai/service/AIServiceTest$should ask question using default provider$1`com/kbuilder/ai/service/AIServiceTest$should ask question using default provider$1$exception$1$1Ycom/kbuilder/ai/service/AIServiceTest$should ask stream question using default provider$1gcom/kbuilder/ai/service/AIServiceTest$should ask stream question using default provider$1$exception$1$1Ccom/kbuilder/ai/service/AIServiceTest$should test all connections$1Gcom/kbuilder/ai/service/AIServiceTest$should test specific connection$1&com/kbuilder/ai/service/MockAIProvider$com/kbuilder/cli/GenerateCommandTest com/kbuilder/cli/KBuilderCliTest%com/kbuilder/config/ConfigurationTestcom/kbuilder/cli/AskCommandTest*com/kbuilder/cli/TestConnectionCommandTest                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      