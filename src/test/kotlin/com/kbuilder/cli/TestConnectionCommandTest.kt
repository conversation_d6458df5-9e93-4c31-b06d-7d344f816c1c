package com.kbuilder.cli

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import picocli.CommandLine
import java.io.ByteArrayOutputStream
import java.io.PrintWriter

class TestConnectionCommandTest {

    @Test
    fun `should print connection test results when test-connection command is executed`() {
        // Arrange
        val outputStream = ByteArrayOutputStream()
        val printWriter = PrintWriter(outputStream)
        val cli = KBuilderCli()
        val commandLine = CommandLine(cli)
        commandLine.out = printWriter
        
        // Act
        val exitCode = commandLine.execute("test-connection")
        
        // Assert
        assertEquals(0, exitCode)
        printWriter.flush()
        val output = outputStream.toString().trim()
        assertTrue(output.contains("Testing AI service connections"))
    }

    @Test
    fun `should test specific provider when provider option is given`() {
        // Arrange
        val outputStream = ByteArrayOutputStream()
        val printWriter = PrintWriter(outputStream)
        val cli = KBuilderCli()
        val commandLine = CommandLine(cli)
        commandLine.out = printWriter
        
        // Act
        val exitCode = commandLine.execute("test-connection", "--provider", "openai")
        
        // Assert
        assertEquals(0, exitCode)
        printWriter.flush()
        val output = outputStream.toString().trim()
        assertTrue(output.contains("Testing connection to provider: openai"))
    }

    @Test
    fun `should show help for test-connection command`() {
        // Arrange
        val outputStream = ByteArrayOutputStream()
        val printWriter = PrintWriter(outputStream)
        val cli = KBuilderCli()
        val commandLine = CommandLine(cli)
        commandLine.out = printWriter
        
        // Act
        val exitCode = commandLine.execute("test-connection", "--help")
        
        // Assert
        assertEquals(0, exitCode)
        printWriter.flush()
        val output = outputStream.toString().trim()
        assertTrue(output.contains("Test AI service connections"))
    }
}
