package com.kbuilder.config

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.io.TempDir
import java.io.File
import java.nio.file.Path

class ConfigurationTest {
    
    @TempDir
    lateinit var tempDir: Path
    
    private lateinit var configManager: ConfigurationManager
    
    @BeforeEach
    fun setUp() {
        // We'll need to modify ConfigurationManager to accept custom config directory for testing
        configManager = ConfigurationManager()
    }
    
    @Test
    fun `should load default config when file does not exist`() {
        // This test will initially pass since we return default config
        val config = configManager.loadConfig()
        assertNotNull(config)
        assertTrue(config.providers.isEmpty())
        assertNull(config.defaultProvider)
    }
    
    @Test
    fun `should create default config file`() {
        val config = configManager.createDefaultConfig()
        
        assertNotNull(config)
        assertEquals("openai", config.defaultProvider)
        assertTrue(config.providers.containsKey("openai"))
        
        val openaiConfig = config.providers["openai"]
        assertNotNull(openaiConfig)
        assertEquals("openai", openaiConfig?.type)
        assertEquals("your-openai-api-key-here", openaiConfig?.apiKey)
    }
    
    @Test
    fun `should save and load config correctly`() {
        val originalConfig = AppConfig(
            defaultProvider = "test",
            providers = mapOf(
                "test" to ProviderConfiguration(
                    type = "test",
                    apiKey = "test-key",
                    timeout = 5000
                )
            )
        )
        
        // This might fail initially if save/load isn't working properly
        configManager.saveConfig(originalConfig)
        val loadedConfig = configManager.loadConfig()
        
        assertEquals(originalConfig.defaultProvider, loadedConfig.defaultProvider)
        assertEquals(originalConfig.providers.size, loadedConfig.providers.size)
        assertEquals(originalConfig.providers["test"]?.apiKey, loadedConfig.providers["test"]?.apiKey)
    }
    
    @Test
    fun `should handle invalid config file gracefully`() {
        // Create an invalid config file
        val configFile = File(configManager.getConfigPath())
        configFile.parentFile?.mkdirs()
        configFile.writeText("invalid json content")
        
        // Should return default config and not throw exception
        val config = configManager.loadConfig()
        assertNotNull(config)
        assertTrue(config.providers.isEmpty())
    }
    
    @Test
    fun `should check config file existence`() {
        // Initially should not exist
        assertFalse(configManager.configExists())
        
        // After creating default config, should exist
        configManager.createDefaultConfig()
        assertTrue(configManager.configExists())
    }
}
