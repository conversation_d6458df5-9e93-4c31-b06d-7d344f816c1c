package com.kbuilder.cli

import picocli.CommandLine
import picocli.CommandLine.*
import kotlin.system.exitProcess

@Command(
    name = "kbuilder",
    description = ["AI Coding CLI Tool"],
    mixinStandardHelpOptions = true,
    version = ["0.1.0"],
    subcommands = [GenerateCommand::class, TestConnectionCommand::class, AskCommand::class]
)
class KBuilderCli : Runnable {

    @Spec
    lateinit var spec: CommandLine.Model.CommandSpec

    override fun run() {
        // Default behavior when no subcommand is specified
        spec.commandLine().out.println("KBuilder CLI - Use --help for available commands")
    }
}

@Command(
    name = "generate",
    description = ["Generate code using AI"]
)
class GenerateCommand : Runnable {

    @Spec
    lateinit var spec: CommandLine.Model.CommandSpec

    override fun run() {
        spec.commandLine().out.println("Generate command received.")
    }
}

fun main(args: Array<String>) {
    val exitCode = CommandLine(KBuilderCli()).execute(*args)
    exitProcess(exitCode)
}
