<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.kbuilder.cli.KBuilderCliTest" tests="3" skipped="0" failures="0" errors="0" timestamp="2025-06-17T05:07:58.091Z" hostname="zxnapdeMacBook-Pro.local" time="0.005">
  <properties/>
  <testcase name="should print help message when no arguments provided()" classname="com.kbuilder.cli.KBuilderCliTest" time="0.001"/>
  <testcase name="should print version when --version option is used()" classname="com.kbuilder.cli.KBuilderCliTest" time="0.001"/>
  <testcase name="should print version when -V option is used()" classname="com.kbuilder.cli.KBuilderCliTest" time="0.001"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
