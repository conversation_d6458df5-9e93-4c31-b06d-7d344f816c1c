<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.kbuilder.ai.service.AIServiceTest" tests="7" skipped="0" failures="0" errors="0" timestamp="2025-06-17T05:07:57.928Z" hostname="zxnapdeMacBook-Pro.local" time="0.094">
  <properties/>
  <testcase name="should ask stream question using default provider()" classname="com.kbuilder.ai.service.AIServiceTest" time="0.043"/>
  <testcase name="should test all connections()" classname="com.kbuilder.ai.service.AIServiceTest" time="0.001"/>
  <testcase name="should ask question using default provider()" classname="com.kbuilder.ai.service.AIServiceTest" time="0.001"/>
  <testcase name="should get registered provider()" classname="com.kbuilder.ai.service.AIServiceTest" time="0.001"/>
  <testcase name="should test specific connection()" classname="com.kbuilder.ai.service.AIServiceTest" time="0.001"/>
  <testcase name="should register AI provider successfully()" classname="com.kbuilder.ai.service.AIServiceTest" time="0.043"/>
  <testcase name="should set and get default provider()" classname="com.kbuilder.ai.service.AIServiceTest" time="0.002"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
