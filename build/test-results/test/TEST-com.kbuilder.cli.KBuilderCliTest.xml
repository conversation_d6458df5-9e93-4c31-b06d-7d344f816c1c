<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.kbuilder.cli.KBuilderCliTest" tests="3" skipped="0" failures="0" errors="0" timestamp="2025-06-17T04:56:11.124Z" hostname="zxnapdeMacBook-Pro.local" time="0.043">
  <properties/>
  <testcase name="should print help message when no arguments provided()" classname="com.kbuilder.cli.KBuilderCliTest" time="0.037"/>
  <testcase name="should print version when --version option is used()" classname="com.kbuilder.cli.KBuilderCliTest" time="0.002"/>
  <testcase name="should print version when -V option is used()" classname="com.kbuilder.cli.KBuilderCliTest" time="0.002"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
