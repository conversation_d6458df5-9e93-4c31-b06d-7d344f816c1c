package com.kbuilder.cli

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import picocli.CommandLine
import java.io.ByteArrayOutputStream
import java.io.PrintWriter

class KBuilderCliTest {

    @Test
    fun `should print version when --version option is used`() {
        // Arrange
        val outputStream = ByteArrayOutputStream()
        val printWriter = PrintWriter(outputStream)
        val cli = KBuilderCli()
        val commandLine = CommandLine(cli)
        commandLine.out = printWriter

        // Act
        val exitCode = commandLine.execute("--version")

        // Assert
        assertEquals(0, exitCode)
        printWriter.flush()
        val output = outputStream.toString().trim()
        assertEquals("0.1.0", output)
    }

    @Test
    fun `should print version when -V option is used`() {
        // Arrange
        val outputStream = ByteArrayOutputStream()
        val printWriter = PrintWriter(outputStream)
        val cli = KBuilderCli()
        val commandLine = CommandLine(cli)
        commandLine.out = printWriter

        // Act
        val exitCode = commandLine.execute("-V")

        // Assert
        assertEquals(0, exitCode)
        printWriter.flush()
        val output = outputStream.toString().trim()
        assertEquals("0.1.0", output)
    }

    @Test
    fun `should print help message when no arguments provided`() {
        // Arrange
        val outputStream = ByteArrayOutputStream()
        val printWriter = PrintWriter(outputStream)
        val cli = KBuilderCli()
        val commandLine = CommandLine(cli)
        commandLine.out = printWriter

        // Act
        val exitCode = commandLine.execute()

        // Assert
        assertEquals(0, exitCode)
        printWriter.flush()
        val output = outputStream.toString().trim()
        assertEquals("KBuilder CLI - Use --help for available commands", output)
    }
}
