# KBuilder - AI Coding CLI Tool

A Kotlin-based command-line interface tool for AI-powered code generation, built with Test-Driven Development (TDD) principles.

## Features

- **Version Command**: Display the current version of the tool
- **Generate Command**: AI-powered code generation (placeholder implementation)
- **Modern CLI**: Built with picocli for robust command-line parsing
- **Test Coverage**: Comprehensive test suite using JUnit 5

## Requirements

- Java 17 or higher
- Gradle 8.14.1 (included via wrapper)

## Building the Project

```bash
# Build the project
./gradlew build

# Run tests
./gradlew test

# Run the application
./gradlew run --args="--help"
```

## Usage

### Display Version
```bash
./gradlew run --args="--version"
# or
./gradlew run --args="-V"
```

### Generate Code (Placeholder)
```bash
./gradlew run --args="generate"
```

### Show Help
```bash
./gradlew run --args="--help"
```

## Project Structure

```
├── build.gradle.kts          # Build configuration
├── settings.gradle.kts       # Project settings
├── gradle.properties         # Gradle properties
├── src/
│   ├── main/kotlin/com/kbuilder/cli/
│   │   └── Main.kt           # Main application entry point
│   └── test/kotlin/com/kbuilder/cli/
│       ├── KBuilderCliTest.kt      # CLI tests
│       └── GenerateCommandTest.kt  # Generate command tests
└── README.md                 # This file
```

## Development

This project follows Test-Driven Development (TDD) principles:

1. **Red**: Write failing tests first
2. **Green**: Implement minimal code to make tests pass
3. **Refactor**: Improve code while keeping tests green

### Running Tests

```bash
# Run all tests
./gradlew test

# Run tests with detailed output
./gradlew test --info

# View test reports
open build/reports/tests/test/index.html
```

## Dependencies

- **Kotlin**: 1.9.20
- **picocli**: 4.7.5 (CLI parsing)
- **JUnit 5**: 5.10.0 (Testing framework)
- **AssertJ**: 3.24.2 (Fluent assertions)

## License

This project is part of the KBuilder AI Coding CLI tool development.
