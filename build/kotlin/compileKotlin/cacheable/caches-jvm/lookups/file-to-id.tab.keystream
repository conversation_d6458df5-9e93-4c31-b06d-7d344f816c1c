0src/main/kotlin/com/kbuilder/ai/model/AIModel.kt6src/main/kotlin/com/kbuilder/ai/provider/AIProvider.kt4src/main/kotlin/com/kbuilder/ai/service/AIService.kt(src/main/kotlin/com/kbuilder/cli/Main.kt4src/main/kotlin/com/kbuilder/config/Configuration.ktAsrc/main/kotlin/com/kbuilder/ai/provider/openai/OpenAIProvider.kt/src/main/kotlin/com/kbuilder/http/HttpClient.kt.src/main/kotlin/com/kbuilder/cli/AskCommand.kt9src/main/kotlin/com/kbuilder/cli/TestConnectionCommand.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          